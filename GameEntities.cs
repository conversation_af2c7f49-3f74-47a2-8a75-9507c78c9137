using System;
using System.Collections.Generic;
using System.Drawing;

namespace SnakeGame
{
    public enum Direction
    {
        Up,
        Down,
        Left,
        Right
    }

    public enum PowerUpType
    {
        SpeedBoost,
        ScoreMultiplier,
        Invincibility,
        ExtraLength,
        SlowMotion
    }

    public enum GameState
    {
        Menu,
        Playing,
        Paused,
        GameOver
    }

    public struct Position
    {
        public int X { get; set; }
        public int Y { get; set; }

        public Position(int x, int y)
        {
            X = x;
            Y = y;
        }

        public override bool Equals(object? obj)
        {
            if (obj is Position other)
            {
                return X == other.X && Y == other.Y;
            }
            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(X, Y);
        }

        public static bool operator ==(Position left, Position right)
        {
            return left.Equals(right);
        }

        public static bool operator !=(Position left, Position right)
        {
            return !left.Equals(right);
        }
    }

    public class SnakeSegment
    {
        public Position Position { get; set; }
        public Color Color { get; set; }

        public SnakeSegment(Position position, Color color)
        {
            Position = position;
            Color = color;
        }
    }

    public class Snake
    {
        public List<SnakeSegment> Body { get; private set; }
        public Direction CurrentDirection { get; set; }
        public Direction NextDirection { get; set; }
        public bool IsInvincible { get; set; }
        public DateTime InvincibilityEndTime { get; set; }

        public Snake(Position startPosition)
        {
            Body = new List<SnakeSegment>
            {
                new SnakeSegment(startPosition, Color.Green),
                new SnakeSegment(new Position(startPosition.X - 1, startPosition.Y), Color.LightGreen),
                new SnakeSegment(new Position(startPosition.X - 2, startPosition.Y), Color.LightGreen)
            };
            CurrentDirection = Direction.Right;
            NextDirection = Direction.Right;
            IsInvincible = false;
        }

        public Position GetHead()
        {
            return Body[0].Position;
        }

        public void Move(bool grow = false)
        {
            CurrentDirection = NextDirection;
            Position head = GetHead();
            Position newHead = GetNextPosition(head, CurrentDirection);

            // Add new head
            Body.Insert(0, new SnakeSegment(newHead, Color.Green));

            // Update colors
            for (int i = 1; i < Body.Count; i++)
            {
                Body[i].Color = Color.LightGreen;
            }

            // Remove tail if not growing
            if (!grow && Body.Count > 0)
            {
                Body.RemoveAt(Body.Count - 1);
            }
        }

        private Position GetNextPosition(Position current, Direction direction)
        {
            return direction switch
            {
                Direction.Up => new Position(current.X, current.Y - 1),
                Direction.Down => new Position(current.X, current.Y + 1),
                Direction.Left => new Position(current.X - 1, current.Y),
                Direction.Right => new Position(current.X + 1, current.Y),
                _ => current
            };
        }

        public bool CheckSelfCollision()
        {
            if (IsInvincible && DateTime.Now < InvincibilityEndTime)
                return false;

            Position head = GetHead();
            for (int i = 1; i < Body.Count; i++)
            {
                if (Body[i].Position == head)
                    return true;
            }
            return false;
        }

        public void SetInvincible(int durationMs)
        {
            IsInvincible = true;
            InvincibilityEndTime = DateTime.Now.AddMilliseconds(durationMs);
        }
    }

    public class Food
    {
        public Position Position { get; set; }
        public Color Color { get; set; }
        public int Points { get; set; }

        public Food(Position position, int points = 10)
        {
            Position = position;
            Color = Color.Red;
            Points = points;
        }
    }

    public class PowerUp
    {
        public Position Position { get; set; }
        public PowerUpType Type { get; set; }
        public Color Color { get; set; }
        public DateTime SpawnTime { get; set; }
        public int Duration { get; set; } // in milliseconds
        public bool IsActive { get; set; }

        public PowerUp(Position position, PowerUpType type)
        {
            Position = position;
            Type = type;
            SpawnTime = DateTime.Now;
            IsActive = false;
            Duration = GetDurationForType(type);
            Color = GetColorForType(type);
        }

        private Color GetColorForType(PowerUpType type)
        {
            return type switch
            {
                PowerUpType.SpeedBoost => Color.Yellow,
                PowerUpType.ScoreMultiplier => Color.Purple,
                PowerUpType.Invincibility => Color.Blue,
                PowerUpType.ExtraLength => Color.Orange,
                PowerUpType.SlowMotion => Color.Cyan,
                _ => Color.White
            };
        }

        private int GetDurationForType(PowerUpType type)
        {
            return type switch
            {
                PowerUpType.SpeedBoost => 5000,
                PowerUpType.ScoreMultiplier => 8000,
                PowerUpType.Invincibility => 3000,
                PowerUpType.ExtraLength => 0, // Instant effect
                PowerUpType.SlowMotion => 6000,
                _ => 5000
            };
        }

        public bool ShouldExpire()
        {
            return DateTime.Now.Subtract(SpawnTime).TotalMilliseconds > 15000; // PowerUp expires after 15 seconds if not collected
        }
    }
}
