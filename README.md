# Snake Game with Power-ups

A classic Snake game implementation in C# with Windows Forms, featuring exciting power-ups and enhanced gameplay mechanics.

## Features

### Core Gameplay
- Classic Snake movement with arrow key controls
- Food collection to grow the snake and increase score
- Wall and self-collision detection
- High score tracking
- Pause/Resume functionality

### Power-ups System
The game includes 5 different power-ups that spawn randomly:

1. **🟡 Speed Boost** (Yellow) - Doubles the snake's movement speed for 5 seconds
2. **🟣 Score Multiplier** (Purple) - Doubles points earned from food for 8 seconds  
3. **🔵 Invincibility** (Blue) - Snake can pass through itself for 3 seconds
4. **🟠 Extra Length** (Orange) - Instantly adds 3 segments to the snake + bonus points
5. **🔵 Slow Motion** (<PERSON>an) - Halves the snake's movement speed for 6 seconds

### Visual Effects
- Snake flashes white when invincible
- Power-ups appear as colored diamonds with type indicators
- Real-time status display showing active effects
- Different colors for snake head and body

## Controls

- **Arrow Keys** - Move the snake (Up, Down, Left, Right)
- **Space Bar** - Pause/Resume the game
- **Start Game Button** - Begin a new game
- **Pause Button** - Pause/Resume via mouse click

## How to Run

### Prerequisites
- .NET 6.0 or later with Windows Forms support
- Windows operating system

### Building and Running
1. Open a command prompt in the project directory
2. Run: `dotnet build`
3. Run: `dotnet run`

Alternatively, open the project in Visual Studio and press F5 to run.

## Game Rules

1. **Objective**: Eat food (red circles) to grow your snake and increase your score
2. **Movement**: The snake moves continuously in the current direction
3. **Growth**: Each food item eaten adds one segment to the snake
4. **Collision**: Game ends if the snake hits the walls or itself (unless invincible)
5. **Power-ups**: Collect diamond-shaped power-ups for temporary advantages
6. **Scoring**: 
   - Food: 10 points each (20 with score multiplier)
   - Extra Length power-up: 50 bonus points

## Technical Details

### Architecture
- **GameEngine.cs** - Core game logic and state management
- **GameEntities.cs** - Data structures for Snake, Food, PowerUp, and game objects
- **GameForm.cs** - Windows Forms UI and rendering
- **Program.cs** - Application entry point

### Game Balance
- Base game speed: 150ms per move
- Power-up spawn chance: 2% per game tick
- Maximum power-ups on screen: 3
- Power-up expiration: 15 seconds if not collected
- Grid size: 30x20 cells

## Future Enhancements

Potential improvements for future versions:
- Sound effects and background music
- Multiple difficulty levels
- Additional power-up types
- Animated sprites and particle effects
- Online leaderboards
- Custom themes and skins

## License

This project is open source and available under the MIT License.
