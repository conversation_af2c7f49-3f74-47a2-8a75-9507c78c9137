using System;
using System.Collections.Generic;
using System.Linq;

namespace SnakeGame
{
    public class GameEngine
    {
        public Snake Snake { get; private set; }
        public Food Food { get; private set; }
        public List<PowerUp> PowerUps { get; private set; }
        public GameState State { get; set; }
        public int Score { get; private set; }
        public int HighScore { get; private set; }
        public int GridWidth { get; private set; }
        public int GridHeight { get; private set; }
        public int BaseSpeed { get; private set; }
        public int CurrentSpeed { get; private set; }
        public int ScoreMultiplier { get; private set; }
        public DateTime SpeedBoostEndTime { get; set; }
        public DateTime SlowMotionEndTime { get; set; }
        public DateTime ScoreMultiplierEndTime { get; set; }

        private Random random;

        public GameEngine(int gridWidth, int gridHeight, int baseSpeed = 150)
        {
            GridWidth = gridWidth;
            GridHeight = gridHeight;
            BaseSpeed = baseSpeed;
            CurrentSpeed = baseSpeed;
            ScoreMultiplier = 1;
            random = new Random();
            PowerUps = new List<PowerUp>();
            State = GameState.Menu;
            HighScore = 0;
        }

        public void StartNewGame()
        {
            // Initialize snake in the center
            Position startPos = new Position(GridWidth / 2, GridHeight / 2);
            Snake = new Snake(startPos);
            
            // Reset game state
            Score = 0;
            CurrentSpeed = BaseSpeed;
            ScoreMultiplier = 1;
            PowerUps.Clear();
            State = GameState.Playing;
            
            // Generate initial food
            GenerateFood();
        }

        public void Update()
        {
            if (State != GameState.Playing)
                return;

            // Update power-up effects
            UpdatePowerUpEffects();

            // Move snake
            Snake.Move();

            // Check collisions
            if (CheckWallCollision() || Snake.CheckSelfCollision())
            {
                GameOver();
                return;
            }

            // Check food collision
            if (Snake.GetHead() == Food.Position)
            {
                EatFood();
            }

            // Check power-up collisions
            CheckPowerUpCollisions();

            // Randomly spawn power-ups (higher chance as score increases)
            int spawnChance = Math.Min(5, 1 + Score / 200); // 1-5% chance based on score
            if (random.Next(0, 100) < spawnChance)
            {
                SpawnPowerUp();
            }

            // Remove expired power-ups
            PowerUps.RemoveAll(p => p.ShouldExpire());
        }

        private void UpdatePowerUpEffects()
        {
            // Check if speed boost expired
            if (DateTime.Now > SpeedBoostEndTime)
            {
                CurrentSpeed = BaseSpeed;
            }

            // Check if slow motion expired
            if (DateTime.Now > SlowMotionEndTime)
            {
                CurrentSpeed = BaseSpeed;
            }

            // Check if score multiplier expired
            if (DateTime.Now > ScoreMultiplierEndTime)
            {
                ScoreMultiplier = 1;
            }
        }

        private bool CheckWallCollision()
        {
            Position head = Snake.GetHead();
            return head.X < 0 || head.X >= GridWidth || head.Y < 0 || head.Y >= GridHeight;
        }

        private void EatFood()
        {
            // Grow snake
            Snake.Move(grow: true);

            // Increase score
            Score += Food.Points * ScoreMultiplier;

            // Gradually increase game speed (cap at 50ms minimum)
            if (CurrentSpeed == BaseSpeed) // Only adjust base speed, not power-up speeds
            {
                BaseSpeed = Math.Max(50, BaseSpeed - 2);
                CurrentSpeed = BaseSpeed;
            }

            // Generate new food
            GenerateFood();
        }

        private void CheckPowerUpCollisions()
        {
            Position head = Snake.GetHead();
            for (int i = PowerUps.Count - 1; i >= 0; i--)
            {
                if (PowerUps[i].Position == head)
                {
                    ApplyPowerUp(PowerUps[i]);
                    PowerUps.RemoveAt(i);
                }
            }
        }

        private void ApplyPowerUp(PowerUp powerUp)
        {
            switch (powerUp.Type)
            {
                case PowerUpType.SpeedBoost:
                    CurrentSpeed = BaseSpeed / 2; // Double speed
                    SpeedBoostEndTime = DateTime.Now.AddMilliseconds(powerUp.Duration);
                    break;

                case PowerUpType.ScoreMultiplier:
                    ScoreMultiplier = 2;
                    ScoreMultiplierEndTime = DateTime.Now.AddMilliseconds(powerUp.Duration);
                    break;

                case PowerUpType.Invincibility:
                    Snake.SetInvincible(powerUp.Duration);
                    break;

                case PowerUpType.ExtraLength:
                    // Add 3 extra segments
                    for (int i = 0; i < 3; i++)
                    {
                        Snake.Move(grow: true);
                    }
                    Score += 50; // Bonus points
                    break;

                case PowerUpType.SlowMotion:
                    CurrentSpeed = BaseSpeed * 2; // Half speed
                    SlowMotionEndTime = DateTime.Now.AddMilliseconds(powerUp.Duration);
                    break;
            }
        }

        private void SpawnPowerUp()
        {
            if (PowerUps.Count >= 3) // Limit to 3 power-ups on screen
                return;

            Position position = GenerateRandomPosition();
            if (IsPositionValid(position))
            {
                PowerUpType type = (PowerUpType)random.Next(0, Enum.GetValues(typeof(PowerUpType)).Length);
                PowerUps.Add(new PowerUp(position, type));
            }
        }

        private void GenerateFood()
        {
            Position position;
            do
            {
                position = GenerateRandomPosition();
            } while (!IsPositionValid(position));

            Food = new Food(position);
        }

        private Position GenerateRandomPosition()
        {
            return new Position(random.Next(0, GridWidth), random.Next(0, GridHeight));
        }

        private bool IsPositionValid(Position position)
        {
            // Check if position is occupied by snake
            if (Snake.Body.Any(segment => segment.Position == position))
                return false;

            // Check if position is occupied by food
            if (Food != null && Food.Position == position)
                return false;

            // Check if position is occupied by other power-ups
            if (PowerUps.Any(p => p.Position == position))
                return false;

            return true;
        }

        public void ChangeDirection(Direction newDirection)
        {
            // Prevent snake from going backwards
            if ((Snake.CurrentDirection == Direction.Up && newDirection == Direction.Down) ||
                (Snake.CurrentDirection == Direction.Down && newDirection == Direction.Up) ||
                (Snake.CurrentDirection == Direction.Left && newDirection == Direction.Right) ||
                (Snake.CurrentDirection == Direction.Right && newDirection == Direction.Left))
            {
                return;
            }

            Snake.NextDirection = newDirection;
        }

        public void PauseGame()
        {
            if (State == GameState.Playing)
                State = GameState.Paused;
            else if (State == GameState.Paused)
                State = GameState.Playing;
        }

        private void GameOver()
        {
            State = GameState.GameOver;
            if (Score > HighScore)
            {
                HighScore = Score;
            }
        }
    }
}
