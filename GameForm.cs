using System;
using System.Drawing;
using System.Windows.Forms;

namespace SnakeGame
{
    public partial class GameForm : Form
    {
        private GameEngine gameEngine;
        private System.Windows.Forms.Timer gameTimer;
        private const int CELL_SIZE = 20;
        private const int GRID_WIDTH = 30;
        private const int GRID_HEIGHT = 20;
        private Panel gamePanel;
        private Label scoreLabel;
        private Label highScoreLabel;
        private Label statusLabel;
        private Button startButton;
        private Button pauseButton;

        public GameForm()
        {
            InitializeComponent();
            InitializeGame();
        }

        private void InitializeComponent()
        {
            this.Text = "Snake Game with Power-ups";
            this.Size = new Size(GRID_WIDTH * CELL_SIZE + 200, GRID_HEIGHT * CELL_SIZE + 150);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedSingle;
            this.MaximizeBox = false;
            this.KeyPreview = true;
            this.BackColor = Color.Black;

            // Game panel
            gamePanel = new Panel
            {
                Size = new Size(GRID_WIDTH * CELL_SIZE, GRID_HEIGHT * CELL_SIZE),
                Location = new Point(10, 10),
                BackColor = Color.Black,
                BorderStyle = BorderStyle.FixedSingle
            };
            gamePanel.Paint += GamePanel_Paint;
            this.Controls.Add(gamePanel);

            // Score label
            scoreLabel = new Label
            {
                Text = "Score: 0",
                Location = new Point(GRID_WIDTH * CELL_SIZE + 20, 20),
                Size = new Size(150, 30),
                ForeColor = Color.White,
                Font = new Font("Arial", 12, FontStyle.Bold)
            };
            this.Controls.Add(scoreLabel);

            // High score label
            highScoreLabel = new Label
            {
                Text = "High Score: 0",
                Location = new Point(GRID_WIDTH * CELL_SIZE + 20, 60),
                Size = new Size(150, 30),
                ForeColor = Color.White,
                Font = new Font("Arial", 12, FontStyle.Bold)
            };
            this.Controls.Add(highScoreLabel);

            // Status label
            statusLabel = new Label
            {
                Text = "Press Start to begin!",
                Location = new Point(GRID_WIDTH * CELL_SIZE + 20, 100),
                Size = new Size(150, 60),
                ForeColor = Color.Yellow,
                Font = new Font("Arial", 10, FontStyle.Regular)
            };
            this.Controls.Add(statusLabel);

            // Start button
            startButton = new Button
            {
                Text = "Start Game",
                Location = new Point(GRID_WIDTH * CELL_SIZE + 20, 180),
                Size = new Size(100, 30),
                BackColor = Color.Green,
                ForeColor = Color.White
            };
            startButton.Click += StartButton_Click;
            this.Controls.Add(startButton);

            // Pause button
            pauseButton = new Button
            {
                Text = "Pause",
                Location = new Point(GRID_WIDTH * CELL_SIZE + 20, 220),
                Size = new Size(100, 30),
                BackColor = Color.Orange,
                ForeColor = Color.White,
                Enabled = false
            };
            pauseButton.Click += PauseButton_Click;
            this.Controls.Add(pauseButton);

            // Instructions
            Label instructionsLabel = new Label
            {
                Text = "Controls:\nArrow Keys - Move\nSpace - Pause\n\nPower-ups:\n🟡 Speed Boost\n🟣 Score x2\n🔵 Invincible\n🟠 Extra Length\n🔵 Slow Motion",
                Location = new Point(GRID_WIDTH * CELL_SIZE + 20, 270),
                Size = new Size(150, 200),
                ForeColor = Color.LightGray,
                Font = new Font("Arial", 8, FontStyle.Regular)
            };
            this.Controls.Add(instructionsLabel);

            this.KeyDown += GameForm_KeyDown;
        }

        private void InitializeGame()
        {
            gameEngine = new GameEngine(GRID_WIDTH, GRID_HEIGHT);
            
            gameTimer = new System.Windows.Forms.Timer
            {
                Interval = gameEngine.CurrentSpeed
            };
            gameTimer.Tick += GameTimer_Tick;
        }

        private void StartButton_Click(object sender, EventArgs e)
        {
            gameEngine.StartNewGame();
            gameTimer.Interval = gameEngine.CurrentSpeed;
            gameTimer.Start();
            startButton.Enabled = false;
            pauseButton.Enabled = true;
            statusLabel.Text = "Game in progress...";
            gamePanel.Focus();
        }

        private void PauseButton_Click(object sender, EventArgs e)
        {
            gameEngine.PauseGame();
            if (gameEngine.State == GameState.Paused)
            {
                gameTimer.Stop();
                pauseButton.Text = "Resume";
                statusLabel.Text = "Game paused";
            }
            else
            {
                gameTimer.Start();
                pauseButton.Text = "Pause";
                statusLabel.Text = "Game in progress...";
            }
        }

        private void GameForm_KeyDown(object sender, KeyEventArgs e)
        {
            if (gameEngine.State != GameState.Playing)
                return;

            switch (e.KeyCode)
            {
                case Keys.Up:
                    gameEngine.ChangeDirection(Direction.Up);
                    break;
                case Keys.Down:
                    gameEngine.ChangeDirection(Direction.Down);
                    break;
                case Keys.Left:
                    gameEngine.ChangeDirection(Direction.Left);
                    break;
                case Keys.Right:
                    gameEngine.ChangeDirection(Direction.Right);
                    break;
                case Keys.Space:
                    PauseButton_Click(sender, e);
                    break;
            }
        }

        private void GameTimer_Tick(object sender, EventArgs e)
        {
            gameEngine.Update();
            
            // Update timer interval if speed changed
            if (gameTimer.Interval != gameEngine.CurrentSpeed)
            {
                gameTimer.Interval = gameEngine.CurrentSpeed;
            }

            // Update UI
            scoreLabel.Text = $"Score: {gameEngine.Score}";
            highScoreLabel.Text = $"High Score: {gameEngine.HighScore}";

            // Show speed level
            int speedLevel = (150 - gameEngine.BaseSpeed) / 2 + 1;
            if (speedLevel > 1)
            {
                scoreLabel.Text += $" (Speed: {speedLevel})";
            }

            // Update status based on active power-ups
            UpdateStatusLabel();

            // Check for game over
            if (gameEngine.State == GameState.GameOver)
            {
                gameTimer.Stop();
                startButton.Enabled = true;
                pauseButton.Enabled = false;
                statusLabel.Text = "Game Over!\nPress Start for new game";
            }

            gamePanel.Invalidate(); // Trigger repaint
        }

        private void UpdateStatusLabel()
        {
            string status = "Active effects:\n";
            bool hasEffects = false;

            if (DateTime.Now < gameEngine.SpeedBoostEndTime)
            {
                status += "🟡 Speed Boost\n";
                hasEffects = true;
            }
            if (DateTime.Now < gameEngine.SlowMotionEndTime)
            {
                status += "🔵 Slow Motion\n";
                hasEffects = true;
            }
            if (DateTime.Now < gameEngine.ScoreMultiplierEndTime)
            {
                status += "🟣 Score x2\n";
                hasEffects = true;
            }
            if (gameEngine.Snake?.IsInvincible == true && DateTime.Now < gameEngine.Snake.InvincibilityEndTime)
            {
                status += "🔵 Invincible\n";
                hasEffects = true;
            }

            if (!hasEffects)
            {
                status = "Game in progress...";
            }

            statusLabel.Text = status;
        }

        private void GamePanel_Paint(object sender, PaintEventArgs e)
        {
            Graphics g = e.Graphics;

            if (gameEngine.State == GameState.Menu || gameEngine.Snake == null)
                return;

            // Draw snake
            foreach (var segment in gameEngine.Snake.Body)
            {
                Color segmentColor = segment.Color;

                // Make snake flash when invincible
                if (gameEngine.Snake.IsInvincible && DateTime.Now < gameEngine.Snake.InvincibilityEndTime)
                {
                    if ((DateTime.Now.Millisecond / 100) % 2 == 0)
                        segmentColor = Color.White;
                }

                using (Brush brush = new SolidBrush(segmentColor))
                {
                    g.FillRectangle(brush,
                        segment.Position.X * CELL_SIZE,
                        segment.Position.Y * CELL_SIZE,
                        CELL_SIZE - 1,
                        CELL_SIZE - 1);
                }
            }

            // Draw food
            if (gameEngine.Food != null)
            {
                using (Brush brush = new SolidBrush(gameEngine.Food.Color))
                {
                    g.FillEllipse(brush,
                        gameEngine.Food.Position.X * CELL_SIZE + 2,
                        gameEngine.Food.Position.Y * CELL_SIZE + 2,
                        CELL_SIZE - 4,
                        CELL_SIZE - 4);
                }
            }

            // Draw power-ups
            foreach (var powerUp in gameEngine.PowerUps)
            {
                using (Brush brush = new SolidBrush(powerUp.Color))
                {
                    // Draw power-up as a diamond shape
                    Point[] diamond = {
                        new Point(powerUp.Position.X * CELL_SIZE + CELL_SIZE / 2, powerUp.Position.Y * CELL_SIZE + 2),
                        new Point(powerUp.Position.X * CELL_SIZE + CELL_SIZE - 2, powerUp.Position.Y * CELL_SIZE + CELL_SIZE / 2),
                        new Point(powerUp.Position.X * CELL_SIZE + CELL_SIZE / 2, powerUp.Position.Y * CELL_SIZE + CELL_SIZE - 2),
                        new Point(powerUp.Position.X * CELL_SIZE + 2, powerUp.Position.Y * CELL_SIZE + CELL_SIZE / 2)
                    };
                    g.FillPolygon(brush, diamond);
                }

                // Draw power-up type indicator
                using (Brush textBrush = new SolidBrush(Color.Black))
                using (Font font = new Font("Arial", 8, FontStyle.Bold))
                {
                    string symbol = GetPowerUpSymbol(powerUp.Type);
                    g.DrawString(symbol,
                        font,
                        textBrush,
                        powerUp.Position.X * CELL_SIZE + 6,
                        powerUp.Position.Y * CELL_SIZE + 6);
                }
            }
        }

        private string GetPowerUpSymbol(PowerUpType type)
        {
            return type switch
            {
                PowerUpType.SpeedBoost => "S",
                PowerUpType.ScoreMultiplier => "x2",
                PowerUpType.Invincibility => "I",
                PowerUpType.ExtraLength => "L",
                PowerUpType.SlowMotion => "SM",
                _ => "?"
            };
        }
    }
}
